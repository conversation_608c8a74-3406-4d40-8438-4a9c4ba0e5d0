import tailwindcss from '@tailwindcss/vite';
import { sveltekit } from '@sveltejs/kit/vite';
import houdini from 'houdini/vite';
import { defineConfig, type PluginOption } from 'vite';

// Only run port cleanup in development environment
if (process.env.NODE_ENV !== 'production') {
	try {
		const { execSync } = await import('child_process');
		execSync('lsof -ti:5173 | xargs kill -9', { stdio: 'ignore' });
		console.log('🧹 Cleaned up port 5173');
	} catch {
		// Port was already free, no action needed
	}
}

export default defineConfig(async ({ mode }) => {
	const plugins: PluginOption[] = [
		houdini(), // Must come before sveltekit
		tailwindcss(),
		sveltekit()
	];

	// Only load devtools in development
	if (mode === 'development') {
		const devtoolsJson = (await import('vite-plugin-devtools-json')).default;
		plugins.push(devto<PERSON><PERSON>son() as Plugin);
	}

	return {
		plugins,
		server: {
			port: 5173,
			strictPort: true // This ensures it fails if port 5173 is occupied instead of picking another port
		}
	};
});
