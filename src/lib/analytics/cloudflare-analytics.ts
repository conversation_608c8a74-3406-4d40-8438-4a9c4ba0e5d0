// FREE Cloudflare Analytics for SourceFlex
import type { RequestEvent } from '@sveltejs/kit';

export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  timestamp: number;
  userId?: string;
  sessionId?: string;
  page?: string;
  userAgent?: string;
  country?: string;
  city?: string;
}

/**
 * FREE Cloudflare Analytics Engine integration
 */
export class CloudflareAnalytics {
  
  /**
   * Track page views (FREE)
   */
  static trackPageView(event: RequestEvent, userId?: string): AnalyticsEvent {
    const headers = event.request.headers;
    
    return {
      event: 'page_view',
      properties: {
        path: event.url.pathname,
        search: event.url.search,
        referrer: headers.get('referer'),
        userAgent: headers.get('user-agent'),
        method: event.request.method
      },
      timestamp: Date.now(),
      userId,
      page: event.url.pathname,
      userAgent: headers.get('user-agent') || undefined,
      country: headers.get('cf-ipcountry') || undefined,
      city: headers.get('cf-ipcity') || undefined
    };
  }

  /**
   * Track authentication events (FREE)
   */
  static trackAuthEvent(
    action: 'login' | 'logout' | 'register' | 'password_reset',
    userId?: string,
    metadata?: Record<string, any>
  ): AnalyticsEvent {
    return {
      event: `auth_${action}`,
      properties: {
        action,
        ...metadata
      },
      timestamp: Date.now(),
      userId
    };
  }

  /**
   * Track GraphQL operations (FREE)
   */
  static trackGraphQLOperation(
    operationName: string,
    operationType: 'query' | 'mutation' | 'subscription',
    duration: number,
    success: boolean,
    userId?: string
  ): AnalyticsEvent {
    return {
      event: 'graphql_operation',
      properties: {
        operationName,
        operationType,
        duration,
        success,
        timestamp: Date.now()
      },
      timestamp: Date.now(),
      userId
    };
  }

  /**
   * Track performance metrics (FREE)
   */
  static trackPerformance(
    metric: string,
    value: number,
    unit: string = 'ms',
    metadata?: Record<string, any>
  ): AnalyticsEvent {
    return {
      event: 'performance_metric',
      properties: {
        metric,
        value,
        unit,
        ...metadata
      },
      timestamp: Date.now()
    };
  }

  /**
   * Track errors (FREE)
   */
  static trackError(
    error: Error,
    context?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): AnalyticsEvent {
    return {
      event: 'error',
      properties: {
        message: error.message,
        stack: error.stack,
        context,
        ...metadata
      },
      timestamp: Date.now(),
      userId
    };
  }

  /**
   * Track user interactions (FREE)
   */
  static trackUserInteraction(
    action: string,
    element: string,
    userId?: string,
    metadata?: Record<string, any>
  ): AnalyticsEvent {
    return {
      event: 'user_interaction',
      properties: {
        action,
        element,
        ...metadata
      },
      timestamp: Date.now(),
      userId
    };
  }

  /**
   * Batch events for efficient sending (FREE)
   */
  static batchEvents(events: AnalyticsEvent[]): {
    pageViews: AnalyticsEvent[];
    authEvents: AnalyticsEvent[];
    errors: AnalyticsEvent[];
    performance: AnalyticsEvent[];
    interactions: AnalyticsEvent[];
  } {
    return {
      pageViews: events.filter(e => e.event === 'page_view'),
      authEvents: events.filter(e => e.event.startsWith('auth_')),
      errors: events.filter(e => e.event === 'error'),
      performance: events.filter(e => e.event === 'performance_metric'),
      interactions: events.filter(e => e.event === 'user_interaction')
    };
  }
}

/**
 * Client-side analytics helper
 */
export function initClientAnalytics() {
  if (typeof window === 'undefined') return;

  // Track page navigation
  let currentPath = window.location.pathname;
  
  const trackNavigation = () => {
    if (window.location.pathname !== currentPath) {
      currentPath = window.location.pathname;
      
      // Send analytics event
      fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'page_view',
          properties: {
            path: currentPath,
            timestamp: Date.now()
          }
        })
      }).catch(console.error);
    }
  };

  // Listen for navigation changes
  window.addEventListener('popstate', trackNavigation);
  
  // Track performance metrics
  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const metrics = {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
        };

        fetch('/api/analytics/performance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(metrics)
        }).catch(console.error);
      }
    }, 1000);
  });
}
