<script lang="ts">
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';

  let email = $state('');
  let isLoading = $state(false);
  let isEmailSent = $state(false);

  onMount(() => {
    const urlEmail = $page.url.searchParams.get('email');
    if (urlEmail) {
      email = urlEmail;
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    isLoading = true;

    try {
      const result = await authActions.sendPasswordResetEmail(email);
      
      if (result.error) {
        toast.error(result.error.message || 'Failed to send reset email');
        return;
      }

      isEmailSent = true;
      toast.success('Password reset email sent! Please check your inbox.');
      
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Forgot Password - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      <h2 class="text-3xl font-bold">Reset your password</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Enter your email address and we'll send you a link to reset your password.
      </p>
    </div>

    {#if isEmailSent}
      <div class="border border-green-200 bg-green-50 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="h-5 w-5 text-green-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800 mb-2">Email sent successfully!</h3>
            <p class="text-sm text-green-700">
              We've sent a password reset link to <strong>{email}</strong>. 
              Please check your inbox and follow the instructions.
            </p>
            <div class="mt-3">
              <a href="/auth/login" class="text-sm font-medium text-green-800 hover:underline">
                Back to login →
              </a>
            </div>
          </div>
        </div>
      </div>
    {:else}
      <form class="space-y-6" onsubmit={handleSubmit}>
        <div class="space-y-2">
          <label for="email" class="text-sm font-medium">Email address</label>
          <Input
            id="email"
            type="email"
            bind:value={email}
            placeholder="Enter your email address"
            disabled={isLoading}
          />
        </div>

        <Button type="submit" class="w-full" disabled={isLoading}>
          {#if isLoading}
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          {:else}
            Send reset link
          {/if}
        </Button>

        <div class="text-center">
          <a href="/auth/login" class="text-sm font-medium text-primary hover:underline">
            Back to login
          </a>
        </div>
      </form>
    {/if}
  </div>
</div>
