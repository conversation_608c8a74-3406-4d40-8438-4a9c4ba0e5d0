// FREE Cloudflare Edge Optimization for SourceFlex

export interface EdgeCacheConfig {
  ttl: number;
  bypassOnCookie?: string[];
  varyOn?: string[];
  tags?: string[];
}

/**
 * FREE Edge optimization utilities
 */
export class EdgeOptimizer {
  
  /**
   * Optimize response for edge caching (FREE)
   */
  static optimizeForEdge(
    response: Response,
    config: EdgeCacheConfig
  ): Response {
    const headers = new Headers(response.headers);
    
    // Set cache control for Cloudflare edge
    headers.set('Cache-Control', `public, max-age=${config.ttl}, s-maxage=${config.ttl}`);
    
    // Add Vary headers for personalization
    if (config.varyOn?.length) {
      headers.set('Vary', config.varyOn.join(', '));
    }
    
    // Add cache tags for purging
    if (config.tags?.length) {
      headers.set('Cache-Tag', config.tags.join(','));
    }
    
    // Bypass cache for authenticated users
    if (config.bypassOnCookie?.length) {
      headers.set('CF-Cache-Bypass-On-Cookie', config.bypassOnCookie.join(','));
    }

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers
    });
  }

  /**
   * Create edge-optimized HTML response
   */
  static createEdgeOptimizedHTML(
    html: string,
    config: {
      preloadResources?: string[];
      criticalCSS?: string;
      deferJS?: boolean;
    } = {}
  ): string {
    let optimizedHTML = html;

    // Add resource preloading
    if (config.preloadResources?.length) {
      const preloadLinks = config.preloadResources
        .map(resource => `<link rel="preload" href="${resource}" as="script">`)
        .join('\n');
      
      optimizedHTML = optimizedHTML.replace(
        '<head>',
        `<head>\n${preloadLinks}`
      );
    }

    // Inline critical CSS
    if (config.criticalCSS) {
      optimizedHTML = optimizedHTML.replace(
        '<head>',
        `<head>\n<style>${config.criticalCSS}</style>`
      );
    }

    // Defer non-critical JavaScript
    if (config.deferJS) {
      optimizedHTML = optimizedHTML.replace(
        /<script(?![^>]*defer)([^>]*)>/g,
        '<script defer$1>'
      );
    }

    return optimizedHTML;
  }

  /**
   * Generate edge-side personalization
   */
  static personalizeContent(
    content: string,
    userContext: {
      country?: string;
      timezone?: string;
      language?: string;
      userAgent?: string;
    }
  ): string {
    let personalizedContent = content;

    // Country-specific content
    if (userContext.country) {
      personalizedContent = personalizedContent.replace(
        /\{\{country\}\}/g,
        userContext.country
      );
    }

    // Timezone-specific content
    if (userContext.timezone) {
      const localTime = new Date().toLocaleString('en-US', {
        timeZone: userContext.timezone
      });
      personalizedContent = personalizedContent.replace(
        /\{\{localTime\}\}/g,
        localTime
      );
    }

    // Language-specific content
    if (userContext.language) {
      personalizedContent = personalizedContent.replace(
        /\{\{language\}\}/g,
        userContext.language
      );
    }

    return personalizedContent;
  }

  /**
   * Optimize images for edge delivery
   */
  static optimizeImageDelivery(
    imageUrl: string,
    deviceHints: {
      viewport?: { width: number; height: number };
      dpr?: number;
      connection?: 'slow-2g' | '2g' | '3g' | '4g';
    }
  ): string {
    const params = new URLSearchParams();
    
    // Adjust quality based on connection
    if (deviceHints.connection) {
      const qualityMap = {
        'slow-2g': 60,
        '2g': 70,
        '3g': 80,
        '4g': 85
      };
      params.set('quality', qualityMap[deviceHints.connection].toString());
    }

    // Adjust size based on viewport
    if (deviceHints.viewport) {
      const maxWidth = Math.min(deviceHints.viewport.width * (deviceHints.dpr || 1), 1920);
      params.set('width', maxWidth.toString());
    }

    return `/api/images/optimize?url=${encodeURIComponent(imageUrl)}&${params.toString()}`;
  }

  /**
   * Create edge-cached API response
   */
  static createCachedAPIResponse(
    data: any,
    config: {
      ttl: number;
      staleWhileRevalidate?: number;
      tags?: string[];
    }
  ): Response {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Cache-Control': `public, max-age=${config.ttl}, s-maxage=${config.ttl}`
    };

    if (config.staleWhileRevalidate) {
      headers['Cache-Control'] += `, stale-while-revalidate=${config.staleWhileRevalidate}`;
    }

    if (config.tags?.length) {
      headers['Cache-Tag'] = config.tags.join(',');
    }

    return new Response(JSON.stringify(data), { headers });
  }
}

/**
 * Edge cache configurations for different content types
 */
export const EDGE_CACHE_CONFIGS = {
  // Static pages (long cache)
  STATIC_PAGE: {
    ttl: 86400, // 24 hours
    tags: ['static', 'page']
  },
  
  // Dynamic pages (short cache)
  DYNAMIC_PAGE: {
    ttl: 300, // 5 minutes
    bypassOnCookie: ['session', 'auth'],
    varyOn: ['Authorization'],
    tags: ['dynamic', 'page']
  },
  
  // API responses (medium cache)
  API_RESPONSE: {
    ttl: 600, // 10 minutes
    varyOn: ['Authorization'],
    tags: ['api']
  },
  
  // User-specific content (private cache)
  USER_CONTENT: {
    ttl: 60, // 1 minute
    bypassOnCookie: ['session'],
    varyOn: ['Authorization', 'Cookie'],
    tags: ['user']
  }
} as const;
