// FREE Cloudflare Security Features for SourceFlex
import type { RequestEvent } from '@sveltejs/kit';

export interface CloudflareSecurityInfo {
  country?: string;
  botScore?: number;
  threatScore?: number;
  clientIP?: string;
  ray?: string;
  asn?: string;
  colo?: string;
}

/**
 * Extract FREE Cloudflare security headers
 */
export function getCloudflareSecurityInfo(request: Request): CloudflareSecurityInfo {
  const headers = request.headers;
  
  return {
    country: headers.get('cf-ipcountry') || undefined,
    botScore: headers.get('cf-bot-score') ? parseInt(headers.get('cf-bot-score')!) : undefined,
    threatScore: headers.get('cf-threat-score') ? parseInt(headers.get('cf-threat-score')!) : undefined,
    clientIP: headers.get('cf-connecting-ip') || undefined,
    ray: headers.get('cf-ray') || undefined,
    asn: headers.get('cf-asn') || undefined,
    colo: headers.get('cf-colo') || undefined
  };
}

/**
 * FREE Security validation using Cloudflare headers
 */
export function validateSecurityHeaders(info: CloudflareSecurityInfo): {
  allowed: boolean;
  reason?: string;
  riskLevel: 'low' | 'medium' | 'high';
} {
  // Bot detection (FREE)
  if (info.botScore !== undefined && info.botScore < 30) {
    return { allowed: false, reason: 'Bot detected', riskLevel: 'high' };
  }

  // Threat score check (FREE)
  if (info.threatScore !== undefined && info.threatScore > 50) {
    return { allowed: false, reason: 'High threat score', riskLevel: 'high' };
  }

  // Determine risk level
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  
  if (info.botScore !== undefined && info.botScore < 50) riskLevel = 'medium';
  if (info.threatScore !== undefined && info.threatScore > 20) riskLevel = 'medium';
  if (info.threatScore !== undefined && info.threatScore > 35) riskLevel = 'high';

  return { allowed: true, riskLevel };
}

/**
 * FREE Security headers for responses
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    // Basic security headers (FREE)
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    
    // CSP for your nHost + Houdini setup
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.nhost.run",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' https:",
      "connect-src 'self' https://*.nhost.run https://*.graphql.us-west-2.nhost.run",
      "frame-ancestors 'none'"
    ].join('; '),
    
    // HSTS (FREE)
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
  };
}

/**
 * Log security events for monitoring
 */
export function logSecurityEvent(
  event: RequestEvent, 
  info: CloudflareSecurityInfo, 
  action: 'allowed' | 'blocked',
  reason?: string
) {
  console.log(`[SECURITY] ${action.toUpperCase()}`, {
    url: event.url.pathname,
    country: info.country,
    botScore: info.botScore,
    threatScore: info.threatScore,
    ray: info.ray,
    reason,
    timestamp: new Date().toISOString()
  });
}
