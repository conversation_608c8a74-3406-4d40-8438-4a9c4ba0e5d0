// FREE Cloudflare Image Optimization for SourceFlex

export interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number; // 1-100
  format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  gravity?: 'auto' | 'center' | 'top' | 'bottom' | 'left' | 'right';
}

/**
 * FREE Cloudflare Image Resizing (available on Workers)
 */
export class CloudflareImageOptimizer {
  
  /**
   * Optimize image using Cloudflare's FREE image resizing
   */
  static optimizeImage(
    imageUrl: string, 
    options: ImageOptimizationOptions = {}
  ): string {
    // Default options for web performance
    const defaultOptions: ImageOptimizationOptions = {
      quality: 85,
      format: 'auto',
      fit: 'scale-down'
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    // Build Cloudflare image transformation URL
    const params = new URLSearchParams();
    
    if (finalOptions.width) params.set('width', finalOptions.width.toString());
    if (finalOptions.height) params.set('height', finalOptions.height.toString());
    if (finalOptions.quality) params.set('quality', finalOptions.quality.toString());
    if (finalOptions.format) params.set('format', finalOptions.format);
    if (finalOptions.fit) params.set('fit', finalOptions.fit);
    if (finalOptions.gravity) params.set('gravity', finalOptions.gravity);

    // Return optimized URL (this would be processed by Cloudflare Workers)
    return `/api/images/optimize?url=${encodeURIComponent(imageUrl)}&${params.toString()}`;
  }

  /**
   * Generate responsive image srcset
   */
  static generateResponsiveSrcSet(
    imageUrl: string,
    widths: number[] = [320, 640, 768, 1024, 1280, 1920],
    options: Omit<ImageOptimizationOptions, 'width'> = {}
  ): string {
    return widths
      .map(width => {
        const optimizedUrl = this.optimizeImage(imageUrl, { ...options, width });
        return `${optimizedUrl} ${width}w`;
      })
      .join(', ');
  }

  /**
   * Get optimized avatar image
   */
  static getOptimizedAvatar(
    imageUrl: string,
    size: number = 64
  ): string {
    return this.optimizeImage(imageUrl, {
      width: size,
      height: size,
      fit: 'cover',
      quality: 90,
      format: 'auto'
    });
  }

  /**
   * Get optimized thumbnail
   */
  static getOptimizedThumbnail(
    imageUrl: string,
    width: number = 300,
    height: number = 200
  ): string {
    return this.optimizeImage(imageUrl, {
      width,
      height,
      fit: 'cover',
      quality: 80,
      format: 'auto'
    });
  }
}

/**
 * Svelte component helper for optimized images
 */
export function createOptimizedImageProps(
  src: string,
  alt: string,
  options: ImageOptimizationOptions = {}
) {
  const optimizedSrc = CloudflareImageOptimizer.optimizeImage(src, options);
  const srcSet = CloudflareImageOptimizer.generateResponsiveSrcSet(src, undefined, options);
  
  return {
    src: optimizedSrc,
    srcset: srcSet,
    alt,
    loading: 'lazy' as const,
    decoding: 'async' as const,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  };
}

/**
 * Common image optimization presets
 */
export const IMAGE_PRESETS = {
  AVATAR: { width: 64, height: 64, fit: 'cover', quality: 90 },
  THUMBNAIL: { width: 300, height: 200, fit: 'cover', quality: 80 },
  HERO: { width: 1920, height: 1080, fit: 'cover', quality: 85 },
  CARD: { width: 400, height: 250, fit: 'cover', quality: 80 },
  GALLERY: { width: 800, height: 600, fit: 'cover', quality: 85 }
} as const;
