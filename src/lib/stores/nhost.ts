import { NhostClient } from '@nhost/nhost-js';
import { browser } from '$app/environment';

// Get environment variables - works in both dev and Cloudflare
const getEnvVar = (key: string, fallback: string = '') => {
  if (browser) {
    // Client-side: use import.meta.env
    return import.meta.env[key] || fallback;
  }
  // Server-side: use process.env or fallback
  return process?.env?.[key] || fallback;
};

const NHOST_SUBDOMAIN = getEnvVar('PUBLIC_NHOST_SUBDOMAIN', 'pttthnqikxdsxmeccqho');
const NHOST_REGION = getEnvVar('PUBLIC_NHOST_REGION', 'us-west-2');

// Create nHost client instance - only in browser to avoid SSR issues
export const nhost = browser ? new NhostClient({
  subdomain: NHOST_SUBDOMAIN,
  region: NHOST_REGION,
  // Enable automatic token refresh
  autoRefreshToken: true
}) : {} as any;
