// FREE Cloudflare Caching for SourceFlex
import type { RequestEvent } from '@sveltejs/kit';

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  staleWhileRevalidate?: number; // Serve stale content while fetching new
  cacheKey?: string;
  tags?: string[];
}

/**
 * FREE Cloudflare Cache API wrapper
 */
export class CloudflareCache {
  
  /**
   * Cache GraphQL responses (FREE)
   */
  static async cacheGraphQLResponse(
    request: Request,
    response: Response,
    config: CacheConfig
  ): Promise<Response> {
    // Only cache GET requests and successful responses
    if (request.method !== 'GET' || !response.ok) {
      return response;
    }

    // Clone response for caching
    const responseClone = response.clone();
    
    // Create cache key
    const cacheKey = config.cacheKey || this.generateCacheKey(request);
    
    // Set cache headers (FREE Cloudflare caching)
    const cachedResponse = new Response(responseClone.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers),
        'Cache-Control': `public, max-age=${config.ttl}, s-maxage=${config.ttl}`,
        'CF-Cache-Status': 'MISS', // Will be updated by Cloudflare
        'X-Cache-Key': cacheKey,
        ...(config.staleWhileRevalidate && {
          'Cache-Control': `public, max-age=${config.ttl}, stale-while-revalidate=${config.staleWhileRevalidate}`
        })
      }
    });

    return cachedResponse;
  }

  /**
   * Cache static content (FREE)
   */
  static cacheStaticContent(response: Response, maxAge: number = 86400): Response {
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers),
        'Cache-Control': `public, max-age=${maxAge}, immutable`,
        'CF-Cache-Status': 'MISS'
      }
    });
  }

  /**
   * Generate cache key for requests
   */
  private static generateCacheKey(request: Request): string {
    const url = new URL(request.url);
    const key = `${request.method}:${url.pathname}:${url.search}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '');
  }

  /**
   * Cache user profile data (with user-specific key)
   */
  static async cacheUserData(
    userId: string,
    data: any,
    ttl: number = 300 // 5 minutes
  ): Promise<Response> {
    const cacheKey = `user:${userId}`;
    
    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': `private, max-age=${ttl}`,
        'X-Cache-Key': cacheKey,
        'Vary': 'Authorization'
      }
    });
  }

  /**
   * Cache GraphQL schema (long-term caching)
   */
  static cacheSchema(response: Response): Response {
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers),
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 1 hour
        'CF-Cache-Status': 'MISS'
      }
    });
  }

  /**
   * Purge cache by tags (FREE with some limitations)
   */
  static async purgeByTags(tags: string[]): Promise<void> {
    // This would typically use Cloudflare API
    // For now, we'll log the purge request
    console.log('[CACHE] Purge requested for tags:', tags);
  }
}

/**
 * Cache configurations for different content types
 */
export const CACHE_CONFIGS = {
  // GraphQL queries (short cache for dynamic data)
  GRAPHQL_QUERY: { ttl: 300, staleWhileRevalidate: 60 }, // 5 min cache, 1 min stale
  
  // User profiles (medium cache)
  USER_PROFILE: { ttl: 600, staleWhileRevalidate: 120 }, // 10 min cache, 2 min stale
  
  // Static content (long cache)
  STATIC_CONTENT: { ttl: 86400 }, // 24 hours
  
  // API responses (short cache)
  API_RESPONSE: { ttl: 180, staleWhileRevalidate: 30 }, // 3 min cache, 30s stale
  
  // Schema/metadata (long cache)
  SCHEMA: { ttl: 3600 } // 1 hour
} as const;
