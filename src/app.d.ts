// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { KVNamespace, DurableObjectNamespace } from '@cloudflare/workers-types';

declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env?: {
				// Add your Cloudflare bindings here
				// Example: YOUR_KV_NAMESPACE: KVNamespace;
				// Example: YOUR_DURABLE_OBJECT_NAMESPACE: DurableObjectNamespace;
			};
			ctx?: ExecutionContext;
			caches?: CacheStorage;
			cf?: IncomingRequestCfProperties;
		}
	}
}

export {};
