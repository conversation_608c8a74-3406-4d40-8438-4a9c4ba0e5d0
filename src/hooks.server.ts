import type { Handle } from '@sveltejs/kit';
import { getCloudflareSecurityInfo, validateSecurityHeaders, getSecurityHeaders, logSecurityEvent } from '$lib/security/cloudflare-security';

// Simple in-memory rate limiting (for demo - use Cloudflare KV in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function isRateLimited(ip: string, endpoint: string, maxRequests: number, windowMs: number): boolean {
  const key = `${ip}:${endpoint}`;
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return false;
  }

  if (record.count >= maxRequests) {
    return true;
  }

  record.count++;
  return false;
}

export const handle: Handle = async ({ event, resolve }) => {
  const { request } = event;
  const url = new URL(request.url);

  // Get FREE Cloudflare security info
  const securityInfo = getCloudflareSecurityInfo(request);

  // Validate security headers
  const validation = validateSecurityHeaders(securityInfo);

  if (!validation.allowed) {
    logSecurityEvent(event, securityInfo, 'blocked', validation.reason);
    return new Response('Access Denied', {
      status: 403,
      headers: getSecurityHeaders()
    });
  }

  // Rate limiting for sensitive endpoints (FREE)
  const clientIP = securityInfo.clientIP || 'unknown';

  if (url.pathname.startsWith('/auth/')) {
    if (isRateLimited(clientIP, 'auth', 10, 60000)) { // 10 requests per minute
      logSecurityEvent(event, securityInfo, 'blocked', 'Rate limited');
      return new Response('Too Many Requests', {
        status: 429,
        headers: {
          ...getSecurityHeaders(),
          'Retry-After': '60'
        }
      });
    }
  }

  // GraphQL endpoint protection
  if (url.pathname.includes('graphql') || url.pathname.startsWith('/api/')) {
    if (isRateLimited(clientIP, 'api', 100, 60000)) { // 100 requests per minute
      return new Response('API Rate Limit Exceeded', {
        status: 429,
        headers: getSecurityHeaders()
      });
    }
  }

  // Log allowed requests with risk level
  if (validation.riskLevel === 'medium' || validation.riskLevel === 'high') {
    logSecurityEvent(event, securityInfo, 'allowed', `Risk level: ${validation.riskLevel}`);
  }

  // Resolve the request
  const response = await resolve(event);

  // Add security headers to all responses (FREE)
  const securityHeaders = getSecurityHeaders();
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Add Cloudflare-specific headers for debugging
  if (securityInfo.ray) {
    response.headers.set('CF-Ray', securityInfo.ray);
  }

  return response;
};
