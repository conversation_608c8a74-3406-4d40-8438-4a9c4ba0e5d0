# Cloudflare Pages configuration for SourceFlex
name = "sourceflex"
compatibility_date = "2024-07-17"
pages_build_output_dir = ".svelte-kit/cloudflare"

# Build configuration is handled by Cloudflare Pages dashboard/CI
# [build] section is not supported for Pages projects

# Environment variables for Cloudflare Pages
[env.production]
vars = { NODE_ENV = "production" }

[env.preview]
vars = { NODE_ENV = "preview" }

# Optional: Custom domain configuration (configure later in dashboard)
# [route]
# pattern = "sourceflex.com"
# zone_name = "sourceflex.com"
