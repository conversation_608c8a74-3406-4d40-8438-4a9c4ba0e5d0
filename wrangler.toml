# Cloudflare Workers configuration for SourceFlex
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2024-07-17"

# Static assets configuration for Workers
[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

# Environment variables
[vars]
NODE_ENV = "production"

# Enable Node.js compatibility if needed
compatibility_flags = ["nodejs_compat"]

# FREE Cloudflare KV for caching (100k reads/day free)
[[kv_namespaces]]
binding = "CACHE"
id = "your_kv_namespace_id"
preview_id = "your_preview_kv_namespace_id"

# FREE Analytics and monitoring
[observability]
enabled = true

# FREE Web Analytics
[analytics_engine_datasets]
[[analytics_engine_datasets]]
binding = "ANALYTICS"

# Optional: Custom domain configuration (configure later in dashboard)
# [route]
# pattern = "sourceflex.com"
# zone_name = "sourceflex.com"
