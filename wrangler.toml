# Cloudflare Workers configuration for SourceFlex
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2024-07-17"

# Static assets configuration for Workers
[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

# Environment variables
[vars]
NODE_ENV = "production"
# Add security headers
SECURITY_HEADERS = "enabled"

# Enable Node.js compatibility if needed
compatibility_flags = ["nodejs_compat"]

# Security Rules Configuration
[rules]
# Rate limiting for auth endpoints
[[rules.rate_limiting]]
path = "/auth/*"
requests_per_minute = 10

# DDoS protection for API routes
[[rules.ddos_protection]]
path = "/api/*"
enabled = true

# Optional: Custom domain configuration (configure later in dashboard)
# [route]
# pattern = "sourceflex.com"
# zone_name = "sourceflex.com"
