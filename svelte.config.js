import adapter from '@sveltejs/adapter-cloudflare';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';
import path from 'path';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	kit: { 
		adapter: adapter({
			// Routes that should be prerendered (for better performance)
			routes: {
				include: ['/', '/pricing', '/about'],
				exclude: ['/dashboard/*', '/api/*']
			}
		}),
		alias: {
			$houdini: path.resolve('.', '$houdini')
		}
	}
};

export default config;
