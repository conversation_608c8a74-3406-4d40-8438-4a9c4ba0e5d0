/** @type {import('houdini').ConfigFile} */
const houdiniConfig = {
	// Temporarily disable watchSchema to use static schema only
	// watchSchema: {
	// 	url: 'https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1',
	// 	headers: {
	// 		// Environment-aware header setting
	// 		...(process?.env?.NHOST_ADMIN_SECRET
	// 			? { 'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET }
	// 			: {})
	// 	}
	// },
	plugins: {
		'houdini-svelte': {}
	},
	schemaPath: './schema.graphql',
	include: [
		'src/**/*.svelte',
		'src/**/*.gql',
		'src/**/*.graphql',
		'src/**/*.ts',
		'src/**/*.js'
	],
	module: 'esm',
	framework: 'kit',
	scalars: {
		uuid: {
			type: 'string',
			unmarshal(val) {
				return val ? val : null;
			},
			marshal(val) {
				return val ? val : null;
			}
		},
		timestamptz: {
			type: 'string',
			unmarshal(val) {
				return val; // Timestamp as ISO string
			},
			marshal(val) {
				return val; // Send timestamp as string
			}
		},
		jsonb: {
			type: 'any',
			unmarshal(val) {
				return typeof val === 'string' ? JSON.parse(val) : val;
			},
			marshal(val) {
				return typeof val === 'object' ? JSON.stringify(val) : val;
			}
		},
		desk_type: {
			type: 'string'
		},
		citext: {
			type: 'string'
		},
		bigint: {
			type: 'number'
		},
		bytea: {
			type: 'string'
		},
		numeric: {
			type: 'number'
		},
		date: {
			type: 'string'
		},
		compensation_frequency: {
			type: 'string'
		},
		experience_level: {
			type: 'string'
		},
		payment_terms: {
			type: 'string'
		},
		ppp_fee_type: {
			type: 'string'
		},
		sharing_type: {
			type: 'string'
		},
		job_access_role: {
			type: 'string'
		},
		job_type: {
			type: 'string'
		},
		job_priority: {
			type: 'string'
		},
		job_status: {
			type: 'string'
		},
		work_arrangement: {
			type: 'string'
		}
	}
};

export default houdiniConfig;
