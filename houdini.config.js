/** @type {import('houdini').ConfigFile} */
const houdiniConfig = {
	watchSchema: {
		url: 'https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1',
		headers: {
			// Environment-aware header setting
			...(process?.env?.NHOST_ADMIN_SECRET
				? { 'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET }
				: {})
		}
	},
	plugins: {
		'houdini-svelte': {}
	},
	schemaPath: './schema.graphql',
	disableIntrospection: true,
	include: [
		'src/**/*.svelte',
		'src/**/*.gql',
		'src/**/*.graphql',
		'src/**/*.ts',
		'src/**/*.js'
	],
	module: 'esm',
	framework: 'kit',
	scalars: {
		uuid: {
			type: 'string'
		},
		timestamptz: {
			type: 'string'
		},
		jsonb: {
			type: 'any'
		},
		desk_type: {
			type: 'string'
		},
		citext: {
			type: 'string'
		},
		bigint: {
			type: 'number'
		},
		bytea: {
			type: 'string'
		},
		numeric: {
			type: 'number'
		},
		date: {
			type: 'string'
		},
		compensation_frequency: {
			type: 'string'
		},
		experience_level: {
			type: 'string'
		},
		payment_terms: {
			type: 'string'
		},
		ppp_fee_type: {
			type: 'string'
		},
		sharing_type: {
			type: 'string'
		},
		job_access_role: {
			type: 'string'
		},
		job_type: {
			type: 'string'
		},
		job_priority: {
			type: 'string'
		},
		job_status: {
			type: 'string'
		},
		work_arrangement: {
			type: 'string'
		}
	}
};

export default houdiniConfig;
