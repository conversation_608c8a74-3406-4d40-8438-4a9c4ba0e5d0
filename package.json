{"name": "sourceflex", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "dev:clean": "npm run kill:5173 && npm run dev", "kill:5173": "lsof -ti:5173 | xargs kill -9 2>/dev/null || true", "build": "vite build", "build:cf": "vite build", "preview": "vite preview", "preview:cf": "npm run build:cf && npx wrangler dev", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "deploy:cf": "npm run build:cf && npx wrangler deploy", "deploy:cf:preview": "npm run build:cf && npx wrangler deploy --env preview"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250715.0", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-cloudflare": "^4.5.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@tanstack/table-core": "^8.21.3", "bits-ui": "^2.8.10", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.5", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "dependencies": {"@melt-ui/pp": "^0.3.2", "@melt-ui/svelte": "^0.86.6", "@nhost/nhost-js": "^3.3.0", "houdini": "^1.5.8", "houdini-svelte": "^2.1.18", "lucide-svelte": "^0.525.0", "svelte-5-french-toast": "^2.0.4", "sveltekit-superforms": "^2.27.1", "zod": "^3.25.76"}}